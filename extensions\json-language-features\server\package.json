{"name": "vscode-json-languageserver", "description": "JSON language server", "version": "1.3.4", "author": "Microsoft Corporation", "license": "MIT", "engines": {"node": "*"}, "bin": {"vscode-json-languageserver": "./bin/vscode-json-languageserver"}, "main": "./out/node/jsonServerMain", "dependencies": {"@vscode/l10n": "^0.0.18", "jsonc-parser": "^3.3.1", "request-light": "^0.8.0", "vscode-json-languageservice": "^5.4.4", "vscode-languageserver": "^10.0.0-next.12", "vscode-uri": "^3.0.8"}, "devDependencies": {"@types/mocha": "^9.1.1", "@types/node": "20.x"}, "scripts": {"prepublishOnly": "npm run clean && npm run compile", "compile": "npx gulp compile-extension:json-language-features-server", "watch": "npx gulp watch-extension:json-language-features-server", "clean": "../../../node_modules/.bin/rimraf out", "install-service-next": "npm install vscode-json-languageservice", "install-service-latest": "npm install vscode-json-languageservice", "install-service-local": "npm link vscode-json-languageservice", "install-server-next": "npm install vscode-languageserver@next", "install-server-local": "npm link vscode-languageserver-server", "version": "git commit -m \"JSON Language Server $npm_package_version\" package.json"}}