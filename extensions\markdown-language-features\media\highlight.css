/*
https://raw.githubusercontent.com/isagalaev/highlight.js/master/src/styles/vs2015.css
*/
/*
 * Visual Studio 2015 dark style
 * Author: <PERSON> <<EMAIL>>
 */


.hljs-keyword,
.hljs-literal,
.hljs-symbol,
.hljs-name {
	color: #569CD6;
}
.hljs-link {
	color: #569CD6;
	text-decoration: underline;
}

.hljs-built_in,
.hljs-type {
	color: #4EC9B0;
}

.hljs-number,
.hljs-class {
	color: #B8D7A3;
}

.hljs-string,
.hljs-meta-string {
	color: #D69D85;
}

.hljs-regexp,
.hljs-template-tag {
	color: #9A5334;
}

.hljs-subst,
.hljs-function,
.hljs-title,
.hljs-params,
.hljs-formula {
	color: #DCDCDC;
}

.hljs-comment,
.hljs-quote {
	color: #57A64A;
	font-style: italic;
}

.hljs-doctag {
	color: #608B4E;
}

.hljs-meta,
.hljs-meta-keyword,
.hljs-tag {
	color: #9B9B9B;
}

.hljs-variable,
.hljs-template-variable {
	color: #BD63C5;
}

.hljs-attr,
.hljs-attribute,
.hljs-builtin-name {
	color: #9CDCFE;
}

.hljs-section {
	color: gold;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}

/*.hljs-code {
	font-family:'Monospace';
}*/

.hljs-bullet,
.hljs-selector-tag,
.hljs-selector-id,
.hljs-selector-class,
.hljs-selector-attr,
.hljs-selector-pseudo {
	color: #D7BA7D;
}

.hljs-addition {
	background-color: var(--vscode-diffEditor-insertedTextBackground, rgba(155, 185, 85, 0.2));
	color: rgb(155, 185, 85);
	display: inline-block;
	width: 100%;
}

.hljs-deletion {
	background: var(--vscode-diffEditor-removedTextBackground, rgba(255, 0, 0, 0.2));
	color: rgb(255, 0, 0);
	display: inline-block;
	width: 100%;
}


/*
From https://raw.githubusercontent.com/isagalaev/highlight.js/master/src/styles/vs.css
*/
/*

Visual Studio-like style based on original C# <AUTHOR> <EMAIL>

*/

.vscode-light .hljs-function,
.vscode-light .hljs-params,
.vscode-light .hljs-number,
.vscode-light .hljs-class  {
	color: inherit;
}

.vscode-light .hljs-comment,
.vscode-light .hljs-quote,
.vscode-light .hljs-number,
.vscode-light .hljs-class,
.vscode-light .hljs-variable {
	color: #008000;
}

.vscode-light .hljs-keyword,
.vscode-light .hljs-selector-tag,
.vscode-light .hljs-name,
.vscode-light .hljs-tag {
	color: #00f;
}

.vscode-light .hljs-built_in,
.vscode-light .hljs-builtin-name {
	color: #007acc;
}

.vscode-light .hljs-string,
.vscode-light .hljs-section,
.vscode-light .hljs-attribute,
.vscode-light .hljs-literal,
.vscode-light .hljs-template-tag,
.vscode-light .hljs-template-variable,
.vscode-light .hljs-type {
	color: #a31515;
}

.vscode-light .hljs-subst,
.vscode-light .hljs-selector-attr,
.vscode-light .hljs-selector-pseudo,
.vscode-light .hljs-meta,
.vscode-light .hljs-meta-keyword {
	color: #2b91af;
}
.vscode-light .hljs-title,
.vscode-light .hljs-doctag {
	color: #808080;
}

.vscode-light .hljs-attr {
	color: #f00;
}

.vscode-light .hljs-symbol,
.vscode-light .hljs-bullet,
.vscode-light .hljs-link {
	color: #00b0e8;
}


.vscode-light .hljs-emphasis {
	font-style: italic;
}

.vscode-light .hljs-strong {
	font-weight: bold;
}
