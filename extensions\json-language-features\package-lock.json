{"name": "json-language-features", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "json-language-features", "version": "1.0.0", "license": "MIT", "dependencies": {"@vscode/extension-telemetry": "^0.9.8", "request-light": "^0.8.0", "vscode-languageclient": "^10.0.0-next.14"}, "devDependencies": {"@types/node": "20.x"}, "engines": {"vscode": "^1.77.0"}}, "node_modules/@microsoft/1ds-core-js": {"version": "4.3.4", "resolved": "https://registry.npmjs.org/@microsoft/1ds-core-js/-/1ds-core-js-4.3.4.tgz", "integrity": "sha512-3gbDUQgAO8EoyQTNcAEkxpuPnioC0May13P1l1l0NKZ128L9Ts/sj8QsfwCRTjHz0HThlA+4FptcAJXNYUy3rg==", "license": "MIT", "dependencies": {"@microsoft/applicationinsights-core-js": "3.3.4", "@microsoft/applicationinsights-shims": "3.0.1", "@microsoft/dynamicproto-js": "^2.0.3", "@nevware21/ts-async": ">= 0.5.2 < 2.x", "@nevware21/ts-utils": ">= 0.11.3 < 2.x"}}, "node_modules/@microsoft/1ds-post-js": {"version": "4.3.4", "resolved": "https://registry.npmjs.org/@microsoft/1ds-post-js/-/1ds-post-js-4.3.4.tgz", "integrity": "sha512-nlKjWricDj0Tn68Dt0P8lX9a+X7LYrqJ6/iSfQwMfDhRIGLqW+wxx8gxS+iGWC/oc8zMQAeiZaemUpCwQcwpRQ==", "license": "MIT", "dependencies": {"@microsoft/1ds-core-js": "4.3.4", "@microsoft/applicationinsights-shims": "3.0.1", "@microsoft/dynamicproto-js": "^2.0.3", "@nevware21/ts-async": ">= 0.5.2 < 2.x", "@nevware21/ts-utils": ">= 0.11.3 < 2.x"}}, "node_modules/@microsoft/applicationinsights-channel-js": {"version": "3.3.4", "resolved": "https://registry.npmjs.org/@microsoft/applicationinsights-channel-js/-/applicationinsights-channel-js-3.3.4.tgz", "integrity": "sha512-Z4nrxYwGKP9iyrYtm7iPQXVOFy4FsEsX0nDKkAi96Qpgw+vEh6NH4ORxMMuES0EollBQ3faJyvYCwckuCVIj0g==", "license": "MIT", "dependencies": {"@microsoft/applicationinsights-common": "3.3.4", "@microsoft/applicationinsights-core-js": "3.3.4", "@microsoft/applicationinsights-shims": "3.0.1", "@microsoft/dynamicproto-js": "^2.0.3", "@nevware21/ts-async": ">= 0.5.2 < 2.x", "@nevware21/ts-utils": ">= 0.11.3 < 2.x"}, "peerDependencies": {"tslib": ">= 1.0.0"}}, "node_modules/@microsoft/applicationinsights-common": {"version": "3.3.4", "resolved": "https://registry.npmjs.org/@microsoft/applicationinsights-common/-/applicationinsights-common-3.3.4.tgz", "integrity": "sha512-4ms16MlIvcP4WiUPqopifNxcWCcrXQJ2ADAK/75uok2mNQe6ZNRsqb/P+pvhUxc8A5HRlvoXPP1ptDSN5Girgw==", "license": "MIT", "dependencies": {"@microsoft/applicationinsights-core-js": "3.3.4", "@microsoft/applicationinsights-shims": "3.0.1", "@microsoft/dynamicproto-js": "^2.0.3", "@nevware21/ts-utils": ">= 0.11.3 < 2.x"}, "peerDependencies": {"tslib": ">= 1.0.0"}}, "node_modules/@microsoft/applicationinsights-core-js": {"version": "3.3.4", "resolved": "https://registry.npmjs.org/@microsoft/applicationinsights-core-js/-/applicationinsights-core-js-3.3.4.tgz", "integrity": "sha512-MummANF0mgKIkdvVvfmHQTBliK114IZLRhTL0X0Ep+zjDwWMHqYZgew0nlFKAl6ggu42abPZFK5afpE7qjtYJA==", "license": "MIT", "dependencies": {"@microsoft/applicationinsights-shims": "3.0.1", "@microsoft/dynamicproto-js": "^2.0.3", "@nevware21/ts-async": ">= 0.5.2 < 2.x", "@nevware21/ts-utils": ">= 0.11.3 < 2.x"}, "peerDependencies": {"tslib": ">= 1.0.0"}}, "node_modules/@microsoft/applicationinsights-shims": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/@microsoft/applicationinsights-shims/-/applicationinsights-shims-3.0.1.tgz", "integrity": "sha512-DKwboF47H1nb33rSUfjqI6ryX29v+2QWcTrRvcQDA32AZr5Ilkr7whOOSsD1aBzwqX0RJEIP1Z81jfE3NBm/Lg==", "license": "MIT", "dependencies": {"@nevware21/ts-utils": ">= 0.9.4 < 2.x"}}, "node_modules/@microsoft/applicationinsights-web-basic": {"version": "3.3.4", "resolved": "https://registry.npmjs.org/@microsoft/applicationinsights-web-basic/-/applicationinsights-web-basic-3.3.4.tgz", "integrity": "sha512-OpEPXr8vU/t/M8T9jvWJzJx/pCyygIiR1nGM/2PTde0wn7anl71Gxl5fWol7K/WwFEORNjkL3CEyWOyDc+28AA==", "license": "MIT", "dependencies": {"@microsoft/applicationinsights-channel-js": "3.3.4", "@microsoft/applicationinsights-common": "3.3.4", "@microsoft/applicationinsights-core-js": "3.3.4", "@microsoft/applicationinsights-shims": "3.0.1", "@microsoft/dynamicproto-js": "^2.0.3", "@nevware21/ts-async": ">= 0.5.2 < 2.x", "@nevware21/ts-utils": ">= 0.11.3 < 2.x"}, "peerDependencies": {"tslib": ">= 1.0.0"}}, "node_modules/@microsoft/dynamicproto-js": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/@microsoft/dynamicproto-js/-/dynamicproto-js-2.0.3.tgz", "integrity": "sha512-JT<PERSON>TU80rMy3mdxOjjpaiDQsTLZ6YSGGqsjURsY6AUQtIj0udlF/jYmhdLZu8693ZIC0T1IwYnFa0+QeiMnziBA==", "license": "MIT", "dependencies": {"@nevware21/ts-utils": ">= 0.10.4 < 2.x"}}, "node_modules/@nevware21/ts-async": {"version": "0.5.4", "resolved": "https://registry.npmjs.org/@nevware21/ts-async/-/ts-async-0.5.4.tgz", "integrity": "sha512-IBTyj29GwGlxfzXw2NPnzty+w0Adx61Eze1/lknH/XIVdxtF9UnOpk76tnrHXWa6j84a1RR9hsOcHQPFv9qJjA==", "license": "MIT", "dependencies": {"@nevware21/ts-utils": ">= 0.11.6 < 2.x"}}, "node_modules/@nevware21/ts-utils": {"version": "0.11.6", "resolved": "https://registry.npmjs.org/@nevware21/ts-utils/-/ts-utils-0.11.6.tgz", "integrity": "sha512-OUUJTh3fnaUSzg9DEHgv3d7jC+DnPL65mIO7RaR+jWve7+MmcgIvF79gY97DPQ4frH+IpNR78YAYd/dW4gK3kg==", "license": "MIT"}, "node_modules/@types/node": {"version": "20.11.24", "resolved": "https://registry.npmjs.org/@types/node/-/node-20.11.24.tgz", "integrity": "sha512-Kza43ewS3xoLgCEpQrsT+xRo/EJej1y0kVYGiLFE1NEODXGzTfwiC6tXTLMQskn1X4/Rjlh0MQUvx9W+L9long==", "dev": true, "dependencies": {"undici-types": "~5.26.4"}}, "node_modules/@vscode/extension-telemetry": {"version": "0.9.8", "resolved": "https://registry.npmjs.org/@vscode/extension-telemetry/-/extension-telemetry-0.9.8.tgz", "integrity": "sha512-7YcKoUvmHlIB8QYCE4FNzt3ErHi9gQPhdCM3ZWtpw1bxPT0I+lMdx52KHlzTNoJzQ2NvMX7HyzyDwBEiMgTrWQ==", "license": "MIT", "dependencies": {"@microsoft/1ds-core-js": "^4.3.4", "@microsoft/1ds-post-js": "^4.3.4", "@microsoft/applicationinsights-web-basic": "^3.3.4"}, "engines": {"vscode": "^1.75.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==", "license": "MIT"}, "node_modules/brace-expansion": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz", "integrity": "sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/minimatch": {"version": "10.0.1", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-10.0.1.tgz", "integrity": "sha512-ethXTt3SGGR+95gudmqJ1eNhRO7eGEGIgYA9vnPatK4/etz2MEVDno5GMCibdMTuBMyElzIlgxMna3K94XDIDQ==", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/request-light": {"version": "0.8.0", "resolved": "https://registry.npmjs.org/request-light/-/request-light-0.8.0.tgz", "integrity": "sha512-bH6E4PMmsEXYrLX6Kr1vu+xI3HproB1vECAwaPSJeroLE1kpWE3HR27uB4icx+6YORu1ajqBJXxuedv8ZQg5Lw=="}, "node_modules/semver": {"version": "7.7.1", "resolved": "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz", "integrity": "sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/undici-types": {"version": "5.26.5", "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-5.26.5.tgz", "integrity": "sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==", "dev": true}, "node_modules/vscode-jsonrpc": {"version": "9.0.0-next.7", "resolved": "https://registry.npmjs.org/vscode-jsonrpc/-/vscode-jsonrpc-9.0.0-next.7.tgz", "integrity": "sha512-7SgnbbbJfYr3off0T2KV/RCMYhVsuLeFPw8l3bkxSiavtoTLsOdu1jyxK3yWbdQuO8QOJC7+no0TXmYjRWSC+g==", "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/vscode-languageclient": {"version": "10.0.0-next.14", "resolved": "https://registry.npmjs.org/vscode-languageclient/-/vscode-languageclient-10.0.0-next.14.tgz", "integrity": "sha512-4m/cpNocRgrAkWc8IH4wd3zllAs16NvMmeGcQxFa6xt+mGXJASIeqp0NAFWKZERKg6ClVgBph+SDSZSVvNZ2oA==", "license": "MIT", "dependencies": {"minimatch": "^10.0.1", "semver": "^7.6.3", "vscode-languageserver-protocol": "3.17.6-next.12"}, "engines": {"vscode": "^1.91.0"}}, "node_modules/vscode-languageserver-protocol": {"version": "3.17.6-next.12", "resolved": "https://registry.npmjs.org/vscode-languageserver-protocol/-/vscode-languageserver-protocol-3.17.6-next.12.tgz", "integrity": "sha512-EqrbwF0glTWD2HiDpFc32pJOr6/bJvyKSfCpRQrKy3XsfdloH4p3o/rNJYcpujM0OVLmPZgl1i9g57z9g2YRJA==", "license": "MIT", "dependencies": {"vscode-jsonrpc": "9.0.0-next.7", "vscode-languageserver-types": "3.17.6-next.6"}}, "node_modules/vscode-languageserver-types": {"version": "3.17.6-next.6", "resolved": "https://registry.npmjs.org/vscode-languageserver-types/-/vscode-languageserver-types-3.17.6-next.6.tgz", "integrity": "sha512-aiJY5/yW+xzw7KPNlwi3gQtddq/3EIn5z8X8nCgJfaiAij2R1APKePngv+MUdLdYJBVTLu+Qa0ODsT+pHgYguQ==", "license": "MIT"}}}