<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Void项目架构分析报告</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 5px;
            margin-top: 30px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
        }
        .mermaid {
            text-align: center;
            margin: 20px 0;
        }
        code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Consol<PERSON>', 'Monaco', monospace;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border-left: 4px solid #3498db;
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin-bottom: 5px;
        }
        .feature-list {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .algorithm-box {
            background-color: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
            margin: 15px 0;
        }
        .note {
            background-color: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
            margin: 10px 0;
        }
        @media print {
            body { font-size: 12pt; }
            .mermaid { page-break-inside: avoid; }
            h1, h2, h3 { page-break-after: avoid; }
        }
    </style>
</head>
<body>
    <h1>Void项目架构分析报告</h1>

    <h2>项目概述</h2>
    <p><strong>Void</strong> 是一个开源的AI代码编辑器，作为Cursor的替代方案。它基于VSCode进行开发，集成了多种AI模型，支持本地部署，提供智能代码编辑、聊天交互、自动补全等功能。</p>

    <h3>核心特性</h3>
    <div class="feature-list">
        <ul>
            <li>🤖 <strong>AI代码助手</strong>: 支持多种AI模型（OpenAI、Anthropic、Ollama等）</li>
            <li>⚡ <strong>快速编辑</strong>: Ctrl+K快速编辑，支持Fast Apply和Slow Apply</li>
            <li>💬 <strong>智能聊天</strong>: 侧边栏聊天面板，支持上下文感知对话</li>
            <li>🔧 <strong>工具集成</strong>: 内置多种开发工具，支持MCP协议</li>
            <li>🎯 <strong>自动补全</strong>: 智能代码补全和建议</li>
            <li>🔒 <strong>隐私保护</strong>: 直接连接AI提供商，不保留用户数据</li>
        </ul>
    </div>

    <h2>技术架构</h2>

    <h3>整体架构设计</h3>
    <p>Void采用了基于VSCode的多进程架构，主要分为以下几个层次：</p>
    <ol>
        <li><strong>用户界面层</strong> (Browser Process): 负责用户交互和界面展示</li>
        <li><strong>核心服务层</strong> (Browser Process): 实现核心业务逻辑</li>
        <li><strong>进程间通信层</strong>: 处理Browser和Main进程间的数据传输</li>
        <li><strong>主进程服务层</strong> (Electron Main): 处理AI API调用和系统级操作</li>
        <li><strong>外部服务层</strong>: 连接各种AI服务提供商</li>
    </ol>

    <h3>1. 整体架构图</h3>
    <div class="mermaid">
graph TB
    %% 用户界面层
    subgraph "用户界面层 (Browser Process)"
        UI[Void编辑器界面]
        Sidebar[侧边栏聊天面板]
        QuickEdit[快速编辑 Ctrl+K]
        Settings[设置面板]
        Autocomplete[自动补全]
    end

    %% 核心服务层
    subgraph "核心服务层 (Browser Process)"
        EditCodeService[editCodeService<br/>代码编辑服务]
        ChatThreadService[chatThreadService<br/>聊天线程管理]
        VoidSettingsService[voidSettingsService<br/>设置管理]
        AutocompleteService[autocompleteService<br/>自动补全服务]
        ToolsService[toolsService<br/>工具服务]
        VoidModelService[voidModelService<br/>模型管理]
    end

    %% 通信层
    subgraph "进程间通信层"
        LLMMessageService[sendLLMMessageService<br/>LLM消息服务]
        Channel[IPC Channel<br/>进程间通信]
    end

    %% 主进程服务层
    subgraph "主进程服务层 (Electron Main)"
        LLMChannel[sendLLMMessageChannel<br/>LLM消息通道]
        LLMImpl[sendLLMMessage.impl<br/>LLM实现]
        MCPChannel[mcpChannel<br/>MCP协议通道]
        MetricsMain[metricsMainService<br/>指标服务]
    end

    %% 外部服务层
    subgraph "外部AI服务"
        OpenAI[OpenAI API]
        Anthropic[Anthropic API]
        Ollama[Ollama本地服务]
        Groq[Groq API]
        MCP[MCP协议服务器]
    end

    %% VSCode基础设施
    subgraph "VSCode基础设施"
        Editor[Monaco编辑器]
        FileSystem[文件系统]
        Workbench[工作台]
        Extensions[扩展系统]
    end

    %% 连接关系
    UI --> EditCodeService
    Sidebar --> ChatThreadService
    QuickEdit --> EditCodeService
    Settings --> VoidSettingsService
    Autocomplete --> AutocompleteService

    EditCodeService --> LLMMessageService
    ChatThreadService --> LLMMessageService
    AutocompleteService --> LLMMessageService
    ToolsService --> LLMMessageService

    LLMMessageService --> Channel
    Channel --> LLMChannel
    LLMChannel --> LLMImpl
    LLMChannel --> MCPChannel

    LLMImpl --> OpenAI
    LLMImpl --> Anthropic
    LLMImpl --> Ollama
    LLMImpl --> Groq
    MCPChannel --> MCP

    EditCodeService --> Editor
    VoidModelService --> FileSystem
    ChatThreadService --> Workbench

    %% 样式
    classDef uiLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef serviceLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000
    classDef commLayer fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000
    classDef mainLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000
    classDef externalLayer fill:#fce4ec,stroke:#880e4f,stroke-width:2px,color:#000
    classDef vscodeLayer fill:#f1f8e9,stroke:#33691e,stroke-width:2px,color:#000

    class UI,Sidebar,QuickEdit,Settings,Autocomplete uiLayer
    class EditCodeService,ChatThreadService,VoidSettingsService,AutocompleteService,ToolsService,VoidModelService serviceLayer
    class LLMMessageService,Channel commLayer
    class LLMChannel,LLMImpl,MCPChannel,MetricsMain mainLayer
    class OpenAI,Anthropic,Ollama,Groq,MCP externalLayer
    class Editor,FileSystem,Workbench,Extensions vscodeLayer
    </div>

    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#f9f9f9',
                primaryTextColor: '#333',
                primaryBorderColor: '#333',
                lineColor: '#333'
            }
        });
    </script>

    <div class="note">
        <strong>说明</strong>: 此HTML文件包含了完整的Mermaid图表。在浏览器中打开后，可以通过浏览器的"打印"功能选择"保存为PDF"或使用Word打开HTML文件来转换为Word格式。
    </div>

</body>
</html>
